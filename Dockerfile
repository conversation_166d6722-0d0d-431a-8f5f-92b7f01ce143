FROM alpine

LABEL maintainer=<EMAIL>

# 环境变量设置
ENV APP_NAME {SERVICE_NAME}
ENV APP_ROOT /opt
ENV APP_PATH $APP_ROOT/$APP_NAME

ENV RUN_MODE pro
ENV GIN_MODE release
ENV TZ Asia/Shanghai

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk add tzdata && cp /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone && mkdir -p $APP_PATH
WORKDIR $APP_PATH

ADD ./main $APP_PATH/
