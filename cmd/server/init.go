package server

import (
	"td-device-api/config"
	httpCtl "td-device-api/internal/controller/http"
	"td-device-api/internal/service"
	"time"

	"gitlab.cloud.tenda.cn/tdpkg/contrib/etcd"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/log"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/rpc"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/rpc/httpclient"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/sentinel"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/traefik"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/util"

	"net/http"

	"golang.org/x/sync/errgroup"
)

var (
	g errgroup.Group
)

var (
	env       string
	project   string
	color     string
	apolloUrl string
	cluster   string
	httpPort  int
)

func Init() {
	// 解析命令行
	util.InitFlag(&env, &project, &color, &apolloUrl, &cluster, &httpPort)

	// 本地开发调试
	if len(apolloUrl) == 0 {
		env = "DEV"
		cluster = "Local"
		color = ""
		apolloUrl = "49.234.32.35:18080"
		httpPort = 8009
		project = "common"
	}

	// 初始化配置
	conf := config.Init(apolloUrl, cluster)
	// 初始化logger
	logger := log.NewLogger(log.WithLogLevel(conf.BaseConf.Base.LogLevel))
	logger.Infof("env: [%s], project: [%s], httpPort: [%d], color: [%s], apolloUrl: [%s], cluster: [%s]",
		env, project, httpPort, color, apolloUrl, cluster)

	// 创建sentinel对象
	st, err := sentinel.New(rpc.ApiTdDeviceApi)
	if err != nil {
		logger.Panic(err)
	}

	// 初始化traefik Register客户端
	tk, err := traefik.New(conf.EtcdConf.Etcd.Endpoints, rpc.ApiTdDeviceApi, traefik.WithServicePort(httpPort), traefik.WithServiceColor(color))
	if err != nil {
		logger.Panic(err)
	}
	tk.HttpRouters.AddTls(conf.BaseConf.Base.DomainMain, conf.BaseConf.Base.DomainSans) // 添加tls
	for _, tenantsDomain := range conf.BaseConf.Base.TenantsDomain {
		tk.HttpRouters.AddTls(tenantsDomain.Main, tenantsDomain.Sans) // 添加定制化APP tls
	}
	tk.HttpMiddlewares.AddCustomMiddlewareAuthUser(env, project, conf.EtcdConf.Etcd.Endpoints) // 用户认证
	tk.HttpMiddlewares.AddCustomMiddlewareDyeUser(env, project, conf.EtcdConf.Etcd.Endpoints)  // 用户染色

	// 注册api服务至traefik网关
	if err = tk.Register(); err != nil {
		logger.Panic(err)
	}

	// 服务注册与发现
	httpClient, register, discovery := serviceRegisterAndDiscovery(conf, logger, env, project, color, httpPort)

	// 初始化service
	err = service.New(logger, conf, httpClient)
	if err != nil {
		logger.Panicf("Device service api New error: %v", err)
	}

	g.Go(func() error {
		if err = httpCtl.Start(logger, conf, httpPort, st); err != nil && err != http.ErrServerClosed {
			logger.Panicf("http Start error: %v", err)
		}
		return nil
	})

	g.Go(func() error {
		sig := <-util.RegisterSignal() // 等待退出信号, 优雅退出
		logger.Warnf("recv stop signal[%s]...", sig.String())

		conf.ApolloCli.Close()
		if err = tk.Close(); err != nil { // 注销traefik etcd上线信息
			logger.Errorf("register close error: %v", err)
		}
		if err = register.Close(); err != nil { // register注销etcd上线信息
			logger.Errorf("register close error: %v", err)
		}
		if err = discovery.Close(); err != nil { // discovery注销etcd上线信息
			logger.Errorf("discovery close error: %v", err)
		}

		return httpCtl.Stop(logger)
	})

	if err = g.Wait(); err != nil {
		logger.Panic("服务运行失败: ", err)
	}
}

func serviceRegisterAndDiscovery(conf *config.ConfigInfo, logger *log.Logger, env, project, color string, httpPort int) (*httpclient.HttpClient, *etcd.ServiceRegister, *etcd.ServiceDiscovery) {
	// 服务注册
	register, err := etcd.InitRegister(env,
		project,
		rpc.ApiTdDeviceApi,
		httpPort,
		conf.EtcdConf.Etcd.Endpoints,
		etcd.WithColor(color))
	if err != nil {
		logger.Panic(err)
	}
	if err = register.Register(); err != nil {
		logger.Panic(err)
	}

	// 初始化etcd Discovery客户端
	discovery, err := etcd.InitDiscovery(env, conf.EtcdConf.Etcd.Endpoints)
	if err != nil {
		logger.Panicf("etcd InitDiscovery error: %v", err)
	}
	// Discovery 发现服务
	err = discovery.Watches(conf.ApplicationConf.Application)
	if err != nil {
		logger.Panicf("etcd Watches error: %v", err)
	}

	// 创建内部服务http客户端
	return httpclient.New(discovery, httpclient.WithReqTimeout(10*time.Second)), register, discovery
}
