base:
  name: td-device-api # 服务名称
  env: dev          # 运行环境
  project: td   # 项目名称
  http_port: 8004   # http端口，不填默认8000
  log_level: debug  # 日志等级，不填默认info级
  domain_main: cloud.tenda.com.cn
  domain_sans: [test.cloud.tenda.com.cn]
  color:

etcd:
  endpoints: [101.35.53.17:2379]
  watch_services: [tdwifi-cloud-svc td-device-svc]   # 监听的服务, 字符串数组，可监听多个服务

sentinel:
  # 熔断器
  circuit_breaker:
    slow_rt_ratio: # 基于慢调用比例熔断
      min_request_amount: 100 # 静默数量，如果当前统计周期内(5s)对资源的访问数量小于静默数量，那么熔断器就处于静默期（不会打开熔断器）
      threshold: 0.2 # 慢调用比例的阈值(小数表示，比如0.1表示10%), 在统计周期内(5s)超过即打开熔断器
      max_allowed_rt_ms: 5000 # 慢调用的最大调用时间(单位为 ms), 如果请求的response time大于该值，那么就是慢调用
  #限流器
  flow_control:
    qps: # 基于QPS限流策略, 即1s内允许多少个请求通过限流器
      threshold: 2500 # qps阈值, 即1s内允许多少请求通过