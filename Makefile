.PHONY: build push clean all build-test push-test clean-test all-test whole whole-test

export GOOS=linux
export GOARCH=amd64
export CGO_ENABLED=0

projectName=td-device-api
commitid=$(shell git rev-parse --short HEAD)
#开发/测试环境仓库
repoTest=${ImgRepoTest}
testImageName=td-home-community-test/${projectName}
testImageWholeName=${repoTest}/${testImageName}:$(branch)-${commitid}

#正式环境仓库
repoPro=${ImgRepo}
proImageName=td-home-community/${projectName}
proImageWholeName=${repoPro}/${proImageName}:$(branch)-${commitid}

build:
	cp docker-compose.tmpl docker-compose.yaml
	sed -i "s#{SERVICE_NAME}#${projectName}#g" docker-compose.yaml
	sed -i "s#{CONTAINER_NAME}#${containerName}#g" docker-compose.yaml
	sed -i "s#{IMAGE_NAME}#${proImageWholeName}#g" docker-compose.yaml
	sed -i "s#{SERVICE_NAME}#${projectName}#g" Dockerfile
	sed -i "s#{env}#$(ev)#g" docker-compose.yaml
	sed -i "s#{color}#$(color)#g" docker-compose.yaml
	sed -i "s#{project}#$(project)#g" docker-compose.yaml
	sed -i "s#{http_port}#$(http_port)#g" docker-compose.yaml
	sed -i "s#{cluster}#$(cluster)#g" docker-compose.yaml
	sed -i "s#{APOLLOURL}#$(APOLLOURL)#g" docker-compose.yaml
	go build -o main main.go
	docker build -t ${proImageWholeName} .

build-all:
	cp docker-compose.tmpl docker-compose.yaml
	cp docker-compose.tmpl ./foreign/docker-compose.yaml
	sed -i "s#{SERVICE_NAME}#${projectName}#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{CONTAINER_NAME}#${containerName}#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{IMAGE_NAME}#${proImageWholeName}#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{SERVICE_NAME}#${projectName}#g" Dockerfile
	sed -i "s#{env}#$(ev)#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{color}#$(color)#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{project}#$(project)#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{http_port}#$(http_port)#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{cluster}#$(cluster_cn)#g" docker-compose.yaml 
	sed -i "s#{cluster}#$(cluster_foreign)#g" ./foreign/docker-compose.yaml
	sed -i "s#{APOLLOURL}#$(APOLLOURL_cn)#g" docker-compose.yaml
	sed -i "s#{APOLLOURL}#$(APOLLOURL_foreign)#g" ./foreign/docker-compose.yaml
	go build -o main main.go
	docker build -t ${proImageWholeName} .

build-all-test:
	cp docker-compose.tmpl docker-compose.yaml
	cp docker-compose.tmpl ./foreign/docker-compose.yaml
	sed -i "s#{SERVICE_NAME}#${projectName}#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{CONTAINER_NAME}#${containerName}#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{IMAGE_NAME}#${testImageWholeName}#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{SERVICE_NAME}#${projectName}#g" Dockerfile
	sed -i "s#{env}#$(ev)#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{color}#$(color)#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{project}#$(project)#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{http_port}#$(http_port)#g" docker-compose.yaml ./foreign/docker-compose.yaml
	sed -i "s#{cluster}#$(cluster_cn)#g" docker-compose.yaml 
	sed -i "s#{cluster}#$(cluster_foreign)#g" ./foreign/docker-compose.yaml
	sed -i "s#{APOLLOURL}#$(APOLLOURL_cn)#g" docker-compose.yaml
	sed -i "s#{APOLLOURL}#$(APOLLOURL_foreign)#g" ./foreign/docker-compose.yaml
	go build -o main main.go
	docker build -t ${testImageWholeName} .

build-test:
	cp docker-compose.tmpl docker-compose.yaml
	sed -i "s#{SERVICE_NAME}#${projectName}#g" docker-compose.yaml
	sed -i "s#{CONTAINER_NAME}#${containerName}#g" docker-compose.yaml
	sed -i "s#{IMAGE_NAME}#${testImageWholeName}#g" docker-compose.yaml
	sed -i "s#{SERVICE_NAME}#${projectName}#g" Dockerfile
	sed -i "s#{env}#$(ev)#g" docker-compose.yaml
	sed -i "s#{color}#$(color)#g" docker-compose.yaml
	sed -i "s#{project}#$(project)#g" docker-compose.yaml
	sed -i "s#{http_port}#$(http_port)#g" docker-compose.yaml
	sed -i "s#{cluster}#$(cluster)#g" docker-compose.yaml
	sed -i "s#{APOLLOURL}#$(APOLLOURL)#g" docker-compose.yaml
	go build -o main main.go
	docker build -t ${testImageWholeName} .


push:
	docker push ${proImageWholeName}

push-test:
	docker push ${testImageWholeName}

clean:
	docker rmi -f ${proImageWholeName}

clean-test:
	docker rmi -f ${testImageWholeName}

all: build push
all-test: build-test push-test
whole: build-all push
whole-test: build-all-test push-test
