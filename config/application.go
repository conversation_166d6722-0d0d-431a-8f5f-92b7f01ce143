package config

import "gitlab.cloud.tenda.cn/tdpkg/contrib/apollo"

type ApplicationConfig struct {
	Application *apollo.ApplicationConf `mapstructure:"application"`

	Ipv6Switch bool `mapstructure:"ipv6_switch"`
}

func (s ApplicationConfig) Model() interface{} {
	return &ApplicationConfig{}
}

func (s ApplicationConfig) NameSpace() string {
	return "application"
}

func (s ApplicationConfig) IsPrivate() bool {
	return true
}
