package config

import (
	"gitlab.cloud.tenda.cn/tdpkg/contrib/apollo"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/rpc"
)

// BaseConfig 基础配置
type BaseConfig struct {
	Base *apollo.BaseConf
}

type EtcdConfig struct {
	Etcd *apollo.EtcdConf
}

type ConfigInfo struct {
	ApolloCli *apollo.Client

	ApplicationConf *ApplicationConfig
	BaseConf        BaseConfig
	EtcdConf        EtcdConfig

	Cluster string
}

func Init(apolloUrl, cluster string) *ConfigInfo {
	apolloCli, err := apollo.New(apolloUrl, rpc.ApiTdDeviceApi, apollo.WithCluster(cluster))
	if err != nil {
		panic(err)
	}
	conf := &ConfigInfo{ApolloCli: apolloCli}

	conf.ApplicationConf = apolloCli.GetCustomApplicationConf(ApplicationConfig{}).(*ApplicationConfig)

	conf.EtcdConf.Etcd = apolloCli.GetEtcdConf()
	conf.BaseConf.Base = apolloCli.GetBaseConf()
	conf.Cluster = cluster
	return conf
}
