package service

import (
	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	errCode "gitlab.cloud.tenda.cn/tdpkg/contrib/err-code"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/errors"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/response"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/rpc"
)

var AiAides *aiAides

type aiAides struct{ *service }

func newAiAidesApi(service *service) {
	AiAides = &aiAides{service: service}
	return
}

const (
	PathUrlTdWifiAiAidesAnalysisContent = "/td/td-device-svc/internal/ai-aides/content/analysis/v1"
)

type AnalysisContentParam struct {
	UserContent string `json:"user_content"`
}

func (d *aiAides) AnalysisContent(ctx *gin.Context, param *AnalysisContentParam) (interface{}, error) {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiAiAidesAnalysisContent, req.BodyJSON(param))
	if err != nil {
		return nil, errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiAiAidesAnalysisContent)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return nil, errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return nil, errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return resp.Data, nil
}
