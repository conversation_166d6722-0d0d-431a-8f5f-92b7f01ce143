package service

import (
	"td-device-api/config"

	"gitlab.cloud.tenda.cn/tdpkg/contrib/errors"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/log"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/rpc/httpclient"
)

type service struct {
	logger     *log.Logger
	conf       *config.ConfigInfo
	httpClient *httpclient.HttpClient
}

func New(logger *log.Logger, conf *config.ConfigInfo, httpClient *httpclient.HttpClient) error {
	if logger == nil || conf == nil || httpClient == nil {
		return errors.New("invalid params")
	}
	api := &service{
		logger:     logger,
		conf:       conf,
		httpClient: httpClient,
	}

	newDeviceApi(api)
	newCloudSvc(api)
	newAiAidesApi(api)
	return nil
}
