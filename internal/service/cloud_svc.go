package service

import (
	"encoding/json"
	"td-device-api/internal/model"

	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/errors"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/response"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/rpc"
)

var CloudSvc *cloudSvc

type cloudSvc struct {
	*service
}

func newCloudSvc(s *service) {
	CloudSvc = &cloudSvc{s}
}

const (
	TdwifiCloudSvcPathGetDevInfoByMac = "/tdwifi/tdwifi-cloud-svc/internal/customer-service/dev-info-by-mac/v1"
)

// GetDeviceBindInfo 获取设备绑定信息
func (c *cloudSvc) GetDeviceBindInfo(ctx *gin.Context, sn, mac string) (interface{}, error) {
	var newData model.DeviceInfoByMacInfoResp
	respData := model.DeviceBindInfoResp{}
	respData.Account.ThirdList = make([]string, 0)

	// 长度限制
	if len(sn) > 32 || len(mac) > 16 {
		return respData, nil
	}

	// 获取请求参数
	res, err := c.httpClient.Get(ctx, rpc.SvcTdwifiCloudSvc, TdwifiCloudSvcPathGetDevInfoByMac, req.QueryParam{"mac": mac, "sn": sn})
	if err != nil {
		return respData, err
	}

	resp := &response.CommonResp{}
	if err := res.ToJSON(resp); err != nil {
		return respData, err
	}

	if resp.Code != 0 {
		return respData, errors.New(resp.Msg.(string))
	}

	resBytes, err := json.Marshal(resp.Data)
	if err != nil {
		return respData, err
	}

	if err := json.Unmarshal(resBytes, &newData); err != nil {
		return respData, err
	}

	respData.Device.IsExist = newData.Device.IsExist
	respData.Account.Phone = newData.Account.Phone
	respData.Account.Email = newData.Account.Email

	if respData.Device.IsExist && respData.Account.Phone == "" && respData.Account.Email == "" && !newData.Account.IsOldAccount {
		// 没有绑定，查询新平台绑定关系
		if newData.Device.MainSN == "" {
			newData.Device.MainSN = sn
		}
		bindInfoReq := &DeviceBindInfoParam{
			Sn:      newData.Device.MainSN,
			DevType: "router", // 新平台设备类型目前只有router
		}
		bindInfo, err := Device.GetDeviceBindInfo(ctx, bindInfoReq)
		if err != nil {
			c.logger.Error("GetDeviceBindInfo", "err", err)
		} else {
			// 解析绑定信息
			if bindInfo != nil {
				var bindInfoData model.DeviceBindInfo
				bindInfoBytes, err := json.Marshal(bindInfo)
				if err != nil {
					c.logger.Error("GetDeviceBindInfo", "err", err)
				} else {
					if err := json.Unmarshal(bindInfoBytes, &bindInfoData); err != nil {
						c.logger.Error("GetDeviceBindInfo", "err", err)
					} else {
						respData.Account.Email = bindInfoData.MainUser.Email
						respData.Account.Phone = bindInfoData.MainUser.Phone
					}
				}
			}
		}
	}
	if newData.Account.ThirdList != nil {
		thirdList := make([]string, 0)
		for _, v := range newData.Account.ThirdList {
			if v == "微信" {
				thirdList = append(thirdList, "WECHAT")
			} else if v == "微博" {
				thirdList = append(thirdList, "WEIBO")
			} else if v == "APPLEID" {
				thirdList = append(thirdList, "APPLE")
			} else {
				thirdList = append(thirdList, v)
			}
		}
		respData.Account.ThirdList = thirdList
	}

	return respData, nil
}
