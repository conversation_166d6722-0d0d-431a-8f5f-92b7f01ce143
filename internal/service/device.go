package service

import (
	"encoding/json"
	"td-device-api/config"
	"td-device-api/internal/model"

	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"github.com/mitchellh/mapstructure"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/context"
	errCode "gitlab.cloud.tenda.cn/tdpkg/contrib/err-code"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/errors"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/response"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/rpc"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/rpc/httpclient"
	"golang.org/x/sync/errgroup"
)

var Device *device

type device struct{ *service }

func newDeviceApi(service *service) {
	Device = &device{service: service}
	return
}

const (
	PathUrlTdWifiCloudSvcGetDeviceList    = "/tdwifi/tdwifi-cloud-svc/internal/device-list/get/v1"
	PathUrlTdWifiCloudSvcGetDeviceAccount = "/tdwifi/tdwifi-cloud-svc/internal/account/get/v1"
	PathUrlTdWifiCloudSvcDeviceConfig     = "/tdwifi/tdwifi-cloud-svc/internal/device/config/v1"
	PathUrlTdWifiCloudSvcGetSharedAccount = "/tdwifi/tdwifi-cloud-svc/internal/shared-account/list/v1"
	PathUrlTdWifiCloudSvcAddSharedAccount = "/tdwifi/tdwifi-cloud-svc/internal/shared-account/add/v1"
	PathUrlTdWifiCloudSvcDelSharedAccount = "/tdwifi/tdwifi-cloud-svc/internal/shared-account/del/v1"

	PathUrlTdWifiDeviceSvcDetailBindInfo = "/td/td-device-svc/internal/device-manage/detail/bind/v1"

	PathUrlTdWifiDeviceSvcGetDeviceList        = "/td/td-device-svc/internal/device-list/get/v1"
	PathUrlTdWifiDeviceSvcAccountBindDevice    = "/td/td-device-svc/internal/account/bind/v1"
	PathUrlTdWifiDeviceSvcQrcodeBindDevice     = "/td/td-device-svc/internal/account/qrcode-bind/v1"
	PathUrlTdWifiDeviceSvcAccountUnBindDevice  = "/td/td-device-svc/internal/account/unbind/v1"
	PathUrlTdWifiDeviceSvcGetDeviceBindAccount = "/td/td-device-svc/internal/account/get/v1"
	PathUrlTdWifiDeviceSvcGetProductIconList   = "/td/td-device-svc/internal/icon/list/product/v1"
	PathUrlTdWifiDeviceSvcGetMacCompany        = "/td/td-device-svc/internal/device-company/get/v1"
	PathUrlTdWifiDeviceSvcGetSharedAccount     = "/td/td-device-svc/internal/shared-account/list/v1"
	PathUrlTdWifiDeviceSvcAddSharedAccount     = "/td/td-device-svc/internal/shared-account/add/v1"
	PathUrlTdWifiDeviceSvcDelSharedAccount     = "/td/td-device-svc/internal/shared-account/del/v1"

	PathUrlTdWifiDeviceSvcMainGetSharedList  = "/td/td-device-svc/internal/shared-manage/main/get/v1"
	PathUrlTdWifiDeviceSvcShareGetSharedList = "/td/td-device-svc/internal/shared-manage/share/get/v1"
	PathUrlTdWifiDeviceSvcInviteSend         = "/td/td-device-svc/internal/shared-manage/invite/send/v1"
	PathUrlTdWifiDeviceSvcInviteUndo         = "/td/td-device-svc/internal/shared-manage/invite/undo/v1"
	PathUrlTdWifiDeviceSvcInviteFeedback     = "/td/td-device-svc/internal/shared-manage/invite/feedback/v1"
	PathUrlTdWifiDeviceSvcPermissionUpdate   = "/td/td-device-svc/internal/shared-manage/permission/update/v1"
	PathUrlTdWifiDeviceSvcShareRemove        = "/td/td-device-svc/internal/shared-manage/share/remove/v1"
)

// 设备类型
const (
	DeviceTypeRouter   = "router"
	DeviceTypeMesh     = "mesh"
	DeviceTypeExtender = "extender"
)

type DeviceListParam struct {
	Uuid string `json:"uuid"`
}

type DeviceListResp struct {
	*model.DeviceLists
}

// getOldDeviceList 获取旧平台设备列表
func getOldDeviceList(ctx *gin.Context, d *device, uuid string) (model.OldDeviceLists, error) {
	var newData model.OldDeviceLists
	res, err := d.httpClient.Get(ctx, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcGetDeviceList,
		req.QueryParam{"uuid": uuid})
	if err != nil {
		return model.OldDeviceLists{}, errors.Wrap(err, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcGetDeviceList)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return model.OldDeviceLists{}, errors.Wrap(errCode.ErrSerialize, res.String())
	}
	resByre, err := json.Marshal(resp.Data)
	if err != nil {
		return model.OldDeviceLists{}, errors.Wrap(err, "Marshal failed!")
	}
	err = json.Unmarshal(resByre, &newData)
	if err != nil {
		return model.OldDeviceLists{}, errors.Wrap(err, "Unmarshal failed!")
	}

	return newData, nil
}

// getNewDeviceList 获取新平台设备列表
func getNewDeviceList(ctx *gin.Context, d *device, uuid string) (model.DeviceLists, error) {
	var newData model.DeviceLists
	newData.RouterDevs = make([]model.WifiDevice, 0)
	newData.MeshDevs = make([]model.WifiDevice, 0)
	newData.ExtenderDevs = make([]model.WifiDevice, 0)
	newData.SharedDevs = make([]model.WifiDevice, 0)
	res, err := d.httpClient.Get(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetDeviceList,
		req.QueryParam{"uuid": uuid})
	if err != nil {
		return model.DeviceLists{}, errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetDeviceList)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return model.DeviceLists{}, errors.Wrap(errCode.ErrSerialize, res.String())
	}

	resByre, err := json.Marshal(resp.Data)
	if err != nil {
		return model.DeviceLists{}, errors.Wrap(err, "Marshal failed!")
	}
	err = json.Unmarshal(resByre, &newData)
	if err != nil {
		return model.DeviceLists{}, errors.Wrap(err, "Unmarshal failed!")
	}

	return newData, nil
}

func (d *device) GetDeviceList(ctx *gin.Context, param *DeviceListParam) (*model.DeviceLists, error) {
	var (
		err        error
		eg         errgroup.Group
		oldList    model.OldDeviceLists
		newList    model.DeviceLists
		deviceList model.DeviceLists
	)
	conf := d.conf.ApolloCli.GetCustomApplicationConf(config.ApplicationConfig{}).(*config.ApplicationConfig)
	deviceList.RouterDevs = make([]model.WifiDevice, 0)
	deviceList.MeshDevs = make([]model.WifiDevice, 0)
	deviceList.ExtenderDevs = make([]model.WifiDevice, 0)
	deviceList.SharedDevs = make([]model.WifiDevice, 0)
	uuid := param.Uuid
	// 1. 查询旧平台设备列表
	eg.Go(func() error {
		oldList, err = getOldDeviceList(ctx, d, uuid)
		if err != nil {
			d.logger.WithContext(ctx).Errorf("Get cloud device list faield. err:%v", err)
			return err
		}
		return err
	})
	// 2. 查询新平台设备列表
	eg.Go(func() error {
		newList, err = getNewDeviceList(ctx, d, uuid)
		if err != nil {
			d.logger.WithContext(ctx).Errorf("Get new device list faield. err:%v", err)
			return err
		}
		return err
	})

	if err = eg.Wait(); err != nil {
		d.logger.WithContext(ctx).Errorf("Request uuid [%s] device list failed. error[%v]", uuid, err)
		return nil, err
	}
	// 3. 封装数据
	deviceList.DevCount = newList.DevCount + oldList.DevCount
	if 0 != len(newList.RouterDevs) {
		deviceList.RouterDevs = append(deviceList.RouterDevs, newList.RouterDevs...)
	}
	if 0 != len(newList.MeshDevs) {
		deviceList.MeshDevs = append(deviceList.MeshDevs, newList.MeshDevs...)
	}
	if 0 != len(newList.ExtenderDevs) {
		deviceList.ExtenderDevs = append(deviceList.ExtenderDevs, newList.ExtenderDevs...)
	}
	if 0 != len(newList.SharedDevs) {
		deviceList.SharedDevs = append(deviceList.SharedDevs, newList.SharedDevs...)
	}
	if 0 != len(oldList.RouterDevs) {
		deviceList.RouterDevs = append(deviceList.RouterDevs, oldList.RouterDevs...)
	}
	if 0 != len(oldList.MeshDevs) {
		deviceList.MeshDevs = append(deviceList.MeshDevs, oldList.MeshDevs...)
	}
	if 0 != len(oldList.SharedDevs) {
		deviceList.SharedDevs = append(deviceList.SharedDevs, oldList.SharedDevs...)
	}

	if !conf.Ipv6Switch {
		for router := range deviceList.RouterDevs {
			deviceList.RouterDevs[router].DevServerIpv6 = ""
		}
		for mesh := range deviceList.MeshDevs {
			deviceList.MeshDevs[mesh].DevServerIpv6 = ""
		}
		for extender := range deviceList.ExtenderDevs {
			deviceList.ExtenderDevs[extender].DevServerIpv6 = ""
		}
		for shared := range deviceList.SharedDevs {
			deviceList.SharedDevs[shared].DevServerIpv6 = ""
		}
	}

	d.logger.WithContext(ctx).Debugf("Device List:router[%d]; mesh[%d]; extender[%d]; shared[%d]",
		len(deviceList.RouterDevs), len(deviceList.MeshDevs), len(deviceList.ExtenderDevs), len(deviceList.SharedDevs))

	// 统计
	go func() {
		projectId := context.GetAppId(ctx)
		appId := context.GetProjectId(ctx)
		retData, err := d.httpClient.TdStatisticSvc.Report(ctx, &httpclient.StatisticReportReq{
			StatisticKey: "user_active_day",
			Project:      projectId,
			Data: httpclient.UserActiveReportData{
				Uid: uuid,
			},
			AppId: appId,
		})

		d.logger.WithContext(ctx).Debugf("Report user active day: %+v", retData)
		if err != nil {
			d.logger.WithContext(ctx).Errorf("Report user active day failed. error[%v]", err)
		}
	}()

	return &deviceList, nil
}

type BindDeviceReq struct {
	Uuid    string `json:"uuid"`
	Sn      string `json:"sn"`
	MeshID  string `json:"mesh_id"`
	DevType string `json:"dev_type"`
	Product string `json:"product"`
}

func (d *device) AccountBindDevice(ctx *gin.Context, param *BindDeviceReq) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcAccountBindDevice,
		req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcAccountBindDevice)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errCode.ErrBindDevice
	}

	return nil
}

type QrcodeBindDeviceReq struct {
	Uuid    string `json:"uuid"`
	Sn      string `json:"sn"`
	Mac     string `json:"mac"`
	Product string `json:"product"`
}

func (d *device) QrcodeAccountBindDevice(ctx *gin.Context, param *QrcodeBindDeviceReq) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcQrcodeBindDevice,
		req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcQrcodeBindDevice)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errCode.ErrBindDevice
	}

	return nil
}

type UnBindDeviceReq struct {
	Uuid    string `json:"uuid"`
	Sn      string `json:"sn"`
	MeshID  string `json:"mesh_id"`
	DevType string `json:"dev_type"`
}

func (d *device) AccountUnBindDevice(ctx *gin.Context, param *UnBindDeviceReq) error {

	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcAccountUnBindDevice,
		req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcAccountUnBindDevice)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errCode.ErrUnbindDevice
	}

	return nil
}

type GetDeviceUuidReq struct {
	Sn      string `json:"sn"`
	MeshID  string `json:"mesh_id"`
	DevType string `json:"dev_type"`
	IsOld   bool   `json:"is_old"`
}

type GetAssociatedAccountResp struct {
	Account string `json:"account"`
	Uuid    string `json:"uuid"`
}

func getOldDeviceAssociatedAccount(ctx *gin.Context, d *device, query req.QueryParam) (interface{}, error) {
	res, err := d.httpClient.Get(ctx, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcGetDeviceAccount, query)
	if err != nil {
		return nil, errors.Wrap(err, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcGetDeviceAccount)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return nil, errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return nil, errCode.ErrNoAssociatedUuid
	}

	return resp.Data, nil
}

func getNewDeviceAssociatedAccount(ctx *gin.Context, d *device, query req.QueryParam) (interface{}, error) {
	res, err := d.httpClient.Get(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetDeviceBindAccount, query)
	if err != nil {
		return nil, errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetDeviceBindAccount)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return nil, errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return nil, errCode.ErrNoAssociatedUuid
	}

	return resp.Data, nil
}

func checkParameter(param *GetDeviceUuidReq) error {
	if (DeviceTypeRouter == param.DevType || DeviceTypeExtender == param.DevType || DeviceTypeMesh == param.DevType) && "" == param.Sn {
		return errCode.ErrParam
	}
	return nil
}

func (d *device) GetDeviceAssociatedAccount(ctx *gin.Context, param *GetDeviceUuidReq) (*GetAssociatedAccountResp, error) {
	var err error
	var data interface{}
	// 1. 校验参数
	if err = checkParameter(param); err != nil {
		return nil, errCode.ErrParam
	}

	// 2. 查询关联账号
	var query req.QueryParam
	if DeviceTypeMesh == param.DevType {
		query = req.QueryParam{"sn": param.Sn, "dev_type": param.DevType, "mesh_id": param.MeshID}
	} else {
		query = req.QueryParam{"sn": param.Sn, "dev_type": param.DevType}
	}
	if param.IsOld {
		if data, err = getOldDeviceAssociatedAccount(ctx, d, query); nil != err {
			d.logger.WithContext(ctx).Errorf("Get old device associated account failed. error [%v]", err)
			return nil, err
		}
	} else {
		if data, err = getNewDeviceAssociatedAccount(ctx, d, query); nil != err {
			d.logger.WithContext(ctx).Errorf("Get new device associated account failed. error [%v]", err)
			return nil, err
		}
	}
	accountResp := &GetAssociatedAccountResp{}
	if err = mapstructure.Decode(data, accountResp); err != nil {
		return nil, err
	}
	if "" == accountResp.Uuid && "" == accountResp.Account {
		d.logger.WithContext(ctx).Errorf("Device [%s] not Associated account.", param.Sn)
		return nil, errCode.ErrNoAssociatedUuid
	}
	d.logger.WithContext(ctx).Debugf("Device [%s] Associated by account [%s].", param.Sn, accountResp.Uuid)

	return accountResp, nil
}

type AccountParam struct {
	Uuid string `json:"uuid"`
}

func (d *device) GetMainAccountSharedList(ctx *gin.Context, param *AccountParam) (interface{}, error) {
	res, err := d.httpClient.Get(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcMainGetSharedList,
		req.QueryParam{"uuid": param.Uuid})
	if err != nil {
		return "", errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcMainGetSharedList)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return "", errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return "", errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return resp.Data, nil
}

func (d *device) GetShareAccountSharedList(ctx *gin.Context, param *AccountParam) (interface{}, error) {
	res, err := d.httpClient.Get(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcShareGetSharedList,
		req.QueryParam{"uuid": param.Uuid})
	if err != nil {
		return "", errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcShareGetSharedList)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return "", errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return "", errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return resp.Data, nil
}

type InvitationParam struct {
	MainUuid     string `json:"main_uuid"`
	ShareAccount string `json:"share_account"`
}

func (d *device) SendShareInvitation(ctx *gin.Context, param *InvitationParam) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcInviteSend,
		req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcInviteSend)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

type UndoInviteParam struct {
	MainUuid  string `json:"main_uuid"`
	ShareUuid string `json:"share_uuid"`
}

func (d *device) UndoShareInvitation(ctx *gin.Context, param *UndoInviteParam) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcInviteUndo,
		req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcInviteUndo)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

type FeedbackParam struct {
	MainUuid  string `json:"main_uuid"`
	ShareUuid string `json:"share_uuid"`
	Result    int64  `json:"result"`
	Data      string `json:"data"`
}

func (d *device) InvitationFeedback(ctx *gin.Context, param *FeedbackParam) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcInviteFeedback, req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcInviteFeedback)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

type PermissionParam struct {
	MainUUID  string `json:"main_uuid"`
	ShareUUID string `json:"share_uuid"`
	Operation int64  `json:"operation"`
	Sn        string `json:"sn"`
	MeshID    string `json:"mesh_id"`
	DevType   string `json:"dev_type"`
	IsOld     bool   `json:"is_old"`
}

func (d *device) UpdateDevicePermission(ctx *gin.Context, param *PermissionParam) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcPermissionUpdate, req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcPermissionUpdate)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

type RemoveParam struct {
	MainUUID  string `json:"main_uuid"`
	ShareUUID string `json:"share_uuid"`
	Operation int64  `json:"operation"`
}

func (d *device) RemoveDevicePermission(ctx *gin.Context, param *RemoveParam) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcShareRemove, req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcShareRemove)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

type DeviceConfig struct {
	Location string `json:"location"`
	Mark     string `json:"mark"`
}

type ConfigParam struct {
	Sn     string       `json:"sn"`
	MeshId string       `json:"mesh_id"`
	Uuid   string       `json:"uuid"`
	Config DeviceConfig `json:"config"`
}

func (d *device) UpdateDeviceConfig(ctx *gin.Context, param *ConfigParam) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcDeviceConfig, req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcDeviceConfig)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

type NewSharedAccountParam struct {
	Sn      string `json:"sn"`
	MeshId  string `json:"mesh_id"`
	DevType string `json:"dev_type"`
	Uuid    string `json:"uuid"`    // 主账号
	Account string `json:"account"` // 授权账号
}

func (d *device) GetNewSharedAccountList(ctx *gin.Context, param *NewSharedAccountParam) (interface{}, error) {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetSharedAccount, req.BodyJSON(param))
	if err != nil {
		return nil, errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetSharedAccount)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return nil, errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return nil, errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return resp.Data, nil
}

func (d *device) AddNewSharedAccount(ctx *gin.Context, param *NewSharedAccountParam) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcAddSharedAccount, req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcAddSharedAccount)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

func (d *device) DelNewSharedAccount(ctx *gin.Context, param *NewSharedAccountParam) error {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcDelSharedAccount, req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcDelSharedAccount)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

type SharedAccountParam struct {
	MeshId  string `json:"mesh_id"`
	Cluster string `json:"cluster"`
	Uuid    string `json:"uuid"`    // 主账号
	Account string `json:"account"` // 授权账号
}

func (d *device) GetSharedAccountList(ctx *gin.Context, param *SharedAccountParam) (interface{}, error) {
	res, err := d.httpClient.Get(ctx, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcGetSharedAccount, req.QueryParam{"mesh_id": param.MeshId, "cluster": d.conf.Cluster})
	if err != nil {
		return nil, errors.Wrap(err, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcGetSharedAccount)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return nil, errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return nil, errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return resp.Data, nil
}

func (d *device) AddSharedAccount(ctx *gin.Context, param *SharedAccountParam) error {
	param.Cluster = d.conf.Cluster
	res, err := d.httpClient.Post(ctx, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcAddSharedAccount, req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcAddSharedAccount)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

func (d *device) DelSharedAccount(ctx *gin.Context, param *SharedAccountParam) error {
	param.Cluster = d.conf.Cluster
	res, err := d.httpClient.Post(ctx, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcDelSharedAccount, req.BodyJSON(param))
	if err != nil {
		return errors.Wrap(err, rpc.SvcTdwifiCloudSvc, PathUrlTdWifiCloudSvcDelSharedAccount)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return nil
}

type GetProductIconParam struct {
	Products []string `json:"products"`
}

func (d *device) GetProductIcon(ctx *gin.Context, param *GetProductIconParam) (interface{}, error) {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetProductIconList, req.BodyJSON(param))
	if err != nil {
		return nil, errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetProductIconList)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return nil, errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return nil, errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return resp.Data, nil
}

type GetDeviceCompanyParam struct {
	Macs []string `json:"macs"`
}

func (d *device) GetDeviceCompany(ctx *gin.Context, param *GetDeviceCompanyParam) (interface{}, error) {
	res, err := d.httpClient.Post(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetMacCompany, req.BodyJSON(param))
	if err != nil {
		return nil, errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcGetMacCompany)
	}
	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return nil, errors.Wrap(errCode.ErrSerialize, res.String())
	}
	if errCode.CommonServerSuccess.Code() != resp.Code {
		return nil, errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return resp.Data, nil
}

type DeviceBindInfoParam struct {
	Sn      string `json:"sn"`
	DevType string `json:"dev_type"`
}

func (d *device) GetDeviceBindInfo(ctx *gin.Context, param *DeviceBindInfoParam) (interface{}, error) {
	res, err := d.httpClient.Get(ctx, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcDetailBindInfo, req.QueryParam{"sn": param.Sn, "dev_type": param.DevType})
	if err != nil {
		return nil, errors.Wrap(err, rpc.SvcTdDeviceSvc, PathUrlTdWifiDeviceSvcDetailBindInfo)
	}

	resp := &response.CommonResp{}
	if err = res.ToJSON(resp); err != nil {
		return nil, errors.Wrap(errCode.ErrSerialize, res.String())
	}

	if errCode.CommonServerSuccess.Code() != resp.Code {
		return nil, errors.NewErrCode(resp.Code, resp.Msg.(string), "")
	}

	return resp.Data, nil
}
