package http

import (
	"strings"

	"github.com/gin-gonic/gin"
	errCode "gitlab.cloud.tenda.cn/tdpkg/contrib/err-code"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/errors"

	"td-device-api/internal/service"

	"gitlab.cloud.tenda.cn/tdpkg/contrib/context"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/response"
)

// getDeviceList
func (c *controller) getDeviceList(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var uuid string
	var exist bool
	uuid, exist = ctx.GetQuery("uuid")
	if !exist {
		response.RespError(ctx, errors.New("Empty uuid."))
		return
	}

	// service层请求参数封装
	param := &service.DeviceListParam{
		Uuid: uuid,
	}

	resp, err := service.Device.GetDeviceList(ctx, param)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, resp)
}

func (c *controller) accountBindDeviceByUuid(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.BindDeviceReq
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}
	err = service.Device.AccountBindDevice(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, nil)
}

func (c *controller) accountQrcodeBindDeviceByUuid(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.QrcodeBindDeviceReq
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}
	err = service.Device.QrcodeAccountBindDevice(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, nil)
}

func (c *controller) accountUnbindDeviceByUuid(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.UnBindDeviceReq
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}
	err = service.Device.AccountUnBindDevice(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, nil)
}

type GetUuidResp struct {
	Uuid    string `json:"uuid"`
	Account string `json:"account"`
}

func (c *controller) getDeviceAssociatedAccount(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.GetDeviceUuidReq
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	resp, err := service.Device.GetDeviceAssociatedAccount(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装
	res := &GetUuidResp{
		Uuid:    resp.Uuid,
		Account: resp.Account,
	}

	response.RespSuccess(ctx, res)
}

func (c *controller) getMainAccountSharedList(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	uuid, exist := ctx.GetQuery("uuid")
	if !exist {
		response.RespError(ctx, errors.New("Empty Uuid"))
		return
	}

	// service层请求参数封装
	param := &service.AccountParam{
		Uuid: uuid,
	}

	resp, err := service.Device.GetMainAccountSharedList(ctx, param)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, resp)
}

func (c *controller) getShareAccountSharedList(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	uuid, exist := ctx.GetQuery("uuid")
	if !exist {
		response.RespError(ctx, errors.New("empty uuid"))
		return
	}

	// service层请求参数封装
	param := &service.AccountParam{
		Uuid: uuid,
	}

	resp, err := service.Device.GetShareAccountSharedList(ctx, param)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, resp)
}

func (c *controller) sendShareInvitation(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.InvitationParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.SendShareInvitation(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, nil)
}

func (c *controller) undoShareInvitation(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.UndoInviteParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.UndoShareInvitation(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, nil)
}

func (c *controller) invitationFeedback(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.FeedbackParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.InvitationFeedback(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, nil)
}

func (c *controller) updateDevicePermission(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.PermissionParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.UpdateDevicePermission(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, nil)
}

func (c *controller) removeDevicePermission(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.RemoveParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.RemoveDevicePermission(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, nil)
}

func (c *controller) updateDeviceConfig(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.ConfigParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.UpdateDeviceConfig(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}
	// 返回值封装

	response.RespSuccess(ctx, nil)
}

func (c *controller) getNewSharedAccountList(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	var req service.NewSharedAccountParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	resp, err := service.Device.GetNewSharedAccountList(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, resp)
}

func (c *controller) addNewSharedAccount(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.NewSharedAccountParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.AddNewSharedAccount(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, nil)
}

func (c *controller) delNewSharedAccount(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.NewSharedAccountParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.DelNewSharedAccount(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, nil)
}

// getSharedAccountList
func (c *controller) getSharedAccountList(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	meshId, exist := ctx.GetQuery("mesh_id")
	if !exist {
		response.RespError(ctx, errors.New("empty mesh_id."))
		return
	}

	// service层请求参数封装
	param := &service.SharedAccountParam{
		MeshId: meshId,
	}

	resp, err := service.Device.GetSharedAccountList(ctx, param)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, resp)
}

func (c *controller) addSharedAccount(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.SharedAccountParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.AddSharedAccount(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, nil)
}

func (c *controller) delSharedAccount(ctx *gin.Context) {
	var err error
	defer func() { // 捕获error, 当error不为空时, 将error设置到context中, 用于gin中间件统一打印错误
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.SharedAccountParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	err = service.Device.DelSharedAccount(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, nil)
}

func (c *controller) getProductIcon(ctx *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.GetProductIconParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}
	resp, err := service.Device.GetProductIcon(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, resp)
}

func (c *controller) getDeviceCompany(ctx *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.GetDeviceCompanyParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}
	resp, err := service.Device.GetDeviceCompany(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, resp)
}

func (c *controller) getDeviceBindInfo(ctx *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	snOrMac := ctx.Query("sn_or_mac")
	if snOrMac == "" {
		response.RespError(ctx, errCode.ErrInvalidParam)
		return
	}

	sn := ""
	mac := ""

	if strings.Contains(snOrMac, ":") || len(snOrMac) == 12 {
		mac = strings.Replace(snOrMac, ":", "", -1)
	} else {
		sn = snOrMac
	}

	resp, err := service.CloudSvc.GetDeviceBindInfo(ctx, sn, mac)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, resp)
}
