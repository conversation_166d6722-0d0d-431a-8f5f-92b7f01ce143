package http

import (
	"context"
	"fmt"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/log"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/sentinel"
	"net/http"
	"td-device-api/config"
	"time"
)

const (
	defaultReadTimeout  = 5
	defaultWriteTimeout = 10
	defaultCloseTimeout = 10
)

var httpServer *http.Server

// Start 启动服务
func Start(logger *log.Logger, conf *config.ConfigInfo, port int, st *sentinel.Sentinel) error {
	ip := "0.0.0.0"
	logger.Infof("正在启动http服务，地址[%s]，监听端口[%d]", ip, port)

	httpServer = &http.Server{
		Addr:         fmt.Sprintf("%s:%d", ip, port),
		Handler:      initRouter(logger, conf, st),
		ReadTimeout:  defaultReadTimeout * time.Second,
		WriteTimeout: defaultWriteTimeout * time.Second,
	}
	return httpServer.ListenAndServe()
}

// Stop 关闭服务
func Stop(logger *log.Logger) error {
	logger.Info("正在关闭http服务")

	ctx, cancel := context.WithTimeout(context.Background(), defaultCloseTimeout*time.Second)
	defer cancel()
	if err := httpServer.Shutdown(ctx); err != nil {
		return err
	}

	logger.Info("http服务成功关闭")
	return nil
}
