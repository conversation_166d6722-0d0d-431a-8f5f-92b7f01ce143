package http

import (
	"net/http"
	"td-device-api/config"

	"github.com/gin-gonic/gin"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/log"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/midware"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/rpc"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/sentinel"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/util"
)

// initRouter 初始化路由
func initRouter(logger *log.Logger, conf *config.ConfigInfo, st *sentinel.Sentinel) *gin.Engine {
	r := gin.New()

	fcStrategy, err := st.FlowControl.NewFlowControlByQpsStrategy(rpc.ApiTdDeviceApi,
		conf.ApplicationConf.Application.Sentinel.FlowControl.Qps.Convert())
	if err != nil {
		logger.Panic(err)
	}
	cbStrategy, err := st.CircuitBreaker.NewCircuitBreakerBySlowRtRatioStrategy(rpc.ApiTdDeviceApi,
		conf.ApplicationConf.Application.Sentinel.CircuitBreaker.SlowRtRatio.Convert())
	if err != nil {
		logger.Panic(err)
	}
	saStrategy, err := st.SystemAdaptive.NewSystemAdaptiveByLoadStrategy(rpc.ApiTdDeviceApi)
	if err != nil {
		logger.Panic(err)
	}
	r.Use(midware.FlowControl(fcStrategy))    // 限流器中间件
	r.Use(midware.CircuitBreaker(cbStrategy)) // 熔断器中间件
	r.Use(midware.SystemAdaptive(saStrategy)) // 系统自适应中间件

	//r.Use(midware.CORS())          // 跨域中间件
	r.Use(gin.Recovery())                        // 异常捕捉
	r.Use(midware.Metric(r, rpc.ApiTdDeviceApi)) // metric
	r.Use(midware.InjectContext())               //上下文注入
	r.Use(midware.Logger())                      // 日志打印

	util.PprofWrap(rpc.ApiTdDeviceApi, r)

	// 项目组
	projectGroup := r.Group("/td")
	// 健康检查
	r.GET("/health/check", func(c *gin.Context) { c.JSON(http.StatusOK, nil) })
	// 服务组, http请求path必须以服务名称为第一个
	serviceGroup := projectGroup.Group("/" + rpc.ApiTdDeviceApi)

	// 初始化路由
	ctl := newController(logger, conf)
	{
		// 用户端设备管理
		serviceGroup.GET("/device-list/get/v1", ctl.getDeviceList)         // APP查询账号的设备列表
		serviceGroup.POST("/account/bind/v1", ctl.accountBindDeviceByUuid) // APP本地连接设备绑定设备
		//serviceGroup.POST("/account/qrcode-bind/v1", ctl.accountQrcodeBindDeviceByUuid) // APP扫描二维码绑定设备(暂不支持该功能)
		serviceGroup.POST("/account/unbind/v1", ctl.accountUnbindDeviceByUuid) // APP云管账号解绑设备
		serviceGroup.POST("/account/get/v1", ctl.getDeviceAssociatedAccount)   // 查询设备云管账号
		serviceGroup.POST("/product/icon/get/v1", ctl.getProductIcon)          // 查询设备图标
		serviceGroup.POST("/device-company/get/v1", ctl.getDeviceCompany)      // 查询设备厂商

		serviceGroup.POST("/new-shared-account/list/v1", ctl.getNewSharedAccountList) // Tenda WiFi v4.1.0 查询旧设备账号授权列表
		serviceGroup.POST("/new-shared-account/add/v1", ctl.addNewSharedAccount)      // Tenda WiFi v4.1.0 添加旧设备账号授权
		serviceGroup.POST("/new-shared-account/del/v1", ctl.delNewSharedAccount)      // Tenda WiFi v4.1.0 撤销旧设备账号授权

		// 共享管理
		serviceGroup.GET("/shared-manage/main/get/v1", ctl.getMainAccountSharedList)         // 主账号获取共享列表
		serviceGroup.GET("/shared-manage/share/get/v1", ctl.getShareAccountSharedList)       // 受邀账号获取共享列表
		serviceGroup.POST("/shared-manage/invite/send/v1", ctl.sendShareInvitation)          // 主账号发送邀请
		serviceGroup.POST("/shared-manage/invite/undo/v1", ctl.undoShareInvitation)          // 主账号撤销邀请
		serviceGroup.POST("/shared-manage/invite/feedback/v1", ctl.invitationFeedback)       // 主账号邀请反馈
		serviceGroup.POST("/shared-manage/permission/update/v1", ctl.updateDevicePermission) // 主账号共享设备权限更新
		serviceGroup.POST("/shared-manage/share/remove/v1", ctl.removeDevicePermission)      // 解除共享关系

		// 旧设备兼容
		serviceGroup.POST("/device/config/v1", ctl.updateDeviceConfig)        // 修改旧设备配置信息
		serviceGroup.GET("/shared-account/list/v1", ctl.getSharedAccountList) // Tenda WiFi v4.0.0 查询旧设备账号授权列表
		serviceGroup.POST("/shared-account/add/v1", ctl.addSharedAccount)     // Tenda WiFi v4.0.0 添加旧设备账号授权
		serviceGroup.POST("/shared-account/del/v1", ctl.delSharedAccount)     // Tenda WiFi v4.0.0 撤销旧设备账号授权

		// 帮助
		serviceGroup.GET("/device-bind-info/get/v1", ctl.getDeviceBindInfo) // 查询设备绑定信息

		// ai语音助手
		serviceGroup.POST("/ai-aides/content/analysis/v1", ctl.analysisContent) // 查询设备绑定信息
	}

	return r
}
