package http

import (
	"github.com/gin-gonic/gin"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/context"
	errCode "gitlab.cloud.tenda.cn/tdpkg/contrib/err-code"
	"gitlab.cloud.tenda.cn/tdpkg/contrib/response"
	"td-device-api/internal/service"
)

func (c *controller) analysisContent(ctx *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			context.SetError(ctx, err)
		}
	}()

	// 请求参数解析
	var req service.AnalysisContentParam
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.RespError(ctx, errCode.ErrSerialize)
		return
	}

	resp, err := service.AiAides.AnalysisContent(ctx, &req)
	if err != nil {
		response.RespError(ctx, err)
		return
	}

	response.RespSuccess(ctx, resp)
}
