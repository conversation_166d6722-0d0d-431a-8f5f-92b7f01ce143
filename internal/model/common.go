package model

type DeviceLists struct {
	DevCount     uint64       `json:"dev_count"`
	RouterDevs   []WifiDevice `json:"router_devs"`
	MeshDevs     []WifiDevice `json:"mesh_devs"`
	ExtenderDevs []WifiDevice `json:"extender_devs"`
	SharedDevs   []WifiDevice `json:"shared_devs"`
}

type OldDeviceLists struct {
	DevCount   uint64       `json:"dev_count"`
	RouterDevs []WifiDevice `json:"router_devs"`
	MeshDevs   []WifiDevice `json:"mesh_devs"`
	SharedDevs []WifiDevice `json:"shared_devs"`
}

type WifiDevice struct {
	DevType       string `json:"dev_type"`
	Sn            string `json:"sn"`
	MeshID        string `json:"mesh_id,omitempty"`
	Mark          string `json:"mark"`
	Location      string `json:"location"`
	State         uint64 `json:"state"`
	Icon          string `json:"icon"`
	BindTime      string `json:"bindtime"`
	Product       string `json:"product"`
	IsOld         bool   `json:"is_old"`
	DevServerIp   string `json:"devserver_ip,omitempty"`
	DevServerIpv6 string `json:"devserver_ipv6,omitempty"`
	DevServerPort string `json:"devserver_port,omitempty"`
	DevAttribute  uint   `json:"dev_attribute"` // 0：默认，路由器   1：cpe设备   2：微企路由器
	// Account       string `json:"account"`
	HomeCompatibleVer string `json:"home_compatible_ver"`
}

type OldDeviceInfo struct {
	IsExist      bool     `json:"is_exist"` // 该SN是否有效
	DevType      string   `json:"dev_type"` // 设备类型 router or mesh
	MainSN       string   `json:"main_sn"`  // 路由器SN 或者mesh设备主节点SN
	MeshId       string   `json:"mesh_id"`
	IsOnline     bool     `json:"is_online"`
	ChildNode    []string `json:"child_node"`     // mesh设备子节点列表
	Product      string   `json:"product"`        // 设备型号
	BindAccount  string   `json:"bind_account"`   // 绑定账号
	IsOldAccount bool     `json:"is_old_account"` // 是否是老账号
	AccountType  string   `json:"account_type"`   // 账号类型 Phone Email QQ WX WB Google Facebook Twitter Apple
}

type AccountInfo struct {
	IsRegister   bool     `json:"is_register"`    // 该账号是否注册
	IsOldAccount bool     `json:"is_old_account"` // 该账号是否是老账号
	Phone        string   `json:"phone"`
	Email        string   `json:"email"`
	Uuid         string   `json:"uuid"`
	ThirdList    []string `json:"third_list"` // 已经绑定的第三方账号，QQ 微信 微博 Google Facebook Apple
	Cluster      string   `json:"cluster"`
	CountryCode  string   `json:"country_code"`
}

type BindInfo struct {
	Uid         string  `json:"uid"`
	ThirdIdent  string  `json:"third_ident"`
	ThirdType   string  `json:"third_type"`
	ThirdAvatar string  `json:"third_avatar"`
	ThirdName   string  `json:"third_name"`
	CreateAt    float64 `json:"create_at"`
}

type DeviceInfoByMacInfoResp struct {
	Device        OldDeviceInfo `json:"device"`
	Cluster       string        `json:"cluster"`
	AccountThirds []*BindInfo   `json:"account_thirds"`
	Account       AccountInfo   `json:"account"`
}

type DeviceBindInfoDevice struct {
	IsExist bool `json:"is_exist"` // 该SN是否有效
}

type DeviceBindInfoAccount struct {
	Phone     string   `json:"phone"`
	Email     string   `json:"email"`
	ThirdList []string `json:"third_list"` // 已经绑定的第三方账号，QQ 微信 微博 Google Facebook Apple
}

type DeviceBindInfoResp struct {
	Device  DeviceBindInfoDevice  `json:"device"`
	Account DeviceBindInfoAccount `json:"account"`
}

// DeviceBindInfo 设备绑定信息
type DeviceBindInfo struct {
	SN            string         `json:"sn"`
	MainUser      UserTinyInfo   `json:"main_user"`       //主账号
	ShareUserList []UserTinyInfo `json:"share_user_list"` //分享用户列表
}

type UserTinyInfo struct {
	UID    string `json:"uid"`
	Name   string `json:"name"`   //用户名
	Avatar string `json:"avatar"` // 头像
	Phone  string `json:"phone"`  //手机号
	Email  string `json:"email"`  //电子邮件
}
