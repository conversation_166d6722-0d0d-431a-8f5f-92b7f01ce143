{"application": {"application.sentinel.circuit_breaker.slow_rt_ratio.max_allowed_rt_ms": "5000", "application.sentinel.circuit_breaker.slow_rt_ratio.min_request_amount": "100", "application.sentinel.circuit_breaker.slow_rt_ratio.threshold": "0.2", "application.sentinel.flow_control.qps.threshold": "2500", "application.watch_project_services.common": "td-device-svc,td-statistic-svc", "application.watch_project_services.tdwifi": "tdwifi-cloud-svc", "ipv6_switch": "true"}, "tenda.base": {"base.all_project_services.common": "td-app-disp,td-device-svc,td-device-api,td-user-svc,td-auth-svc,td-app-msg-svc,td-user-api,td-email-svc,td-delay-svc,td-dyeproxy-api,td-permission-svc,td-sms-svc,td-statistic-svc,td-dev-disp,td-disp-svc,td-employee-svc,td-payment-svc", "base.all_project_services.iot": "td-iot-svc,td-iot-ota-svc,td-iot-thing-svc,td-iot-rule-svc,td-iot-log-svc,td-auth-dvc,td-iot-api,td-iot-protocol-svc,td-iot-dev-access", "base.all_project_services.tdwifi": "td-app-access,tdwifi-app-aggr-api,tdwifi-appaggr-svc,tdwifi-cloud-svc,tdwifi-manage-api,td-dev-access,td-protocol-svc,tdwifi-device-manage-dvc,tdwifi-firmware-svc,tdwifi-mon-rep-sto-svc,tdwifi-upload-dvc,tdwifi-audio-svc", "base.domain_main": "cloud.tenda.com.cn", "base.domain_sans": "test.cloud.tenda.com.cn", "base.host_ip": "0.0.0.0", "base.log_level": "debug", "base.td-sync-center.addr": "https://*************:8200", "base.td-sync-center.ca-cert": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIDVTCCAj2gAwIBAgIJAOPPzWM0g2XoMA0GCSqGSIb3DQEBCwUAMEExCzAJBgNV\nBAYTAkNOMQswCQYDVQQIDAJDRDELMAkGA1UEBwwCQ0QxCzAJBgNVBAoMAlREMQsw\nCQYDVQQLDAJURDAeFw0yMzA2MDYwNjMwMTlaFw0zMzA2MDMwNjMwMTlaMEExCzAJ\nBgNVBAYTAkNOMQswCQYDVQQIDAJDRDELMAkGA1UEBwwCQ0QxCzAJBgNVBAoMAlRE\nMQswCQYDVQQLDAJURDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALoI\nRjQIr6qPnftCPi4C5gp3n7Cc0RnK7N1+u+FNUgDOtD/7qANXg1dGwLDQLd4QUQ6t\nWIDLGq6+Y034VIL+hKthXWtPbqXLPVr1BeC5Ws/uvH69W9NHRGywSBLtmWoV/cz7\ndjS+cmMs8qA9V2Z7mi+4CnFJqQ2mL0uzkB7ZFoQgcEBsgXKPkPaHM4c+2DQH32x+\n2d2eEWNUip5NTs9qDJkHR3HiNo3xZDIzms8pCWmQueJbt16InKFA+yWoJY3jmVM9\n/JwppgbZUela0Uz8iIX+5ZIekuqdvEV/KvjRkyhw/QvQD+udBchCDULjLTHbqoU+\nBWr8YhAD5O1zZ+QTTq0CAwEAAaNQME4wHQYDVR0OBBYEFAFV+jRT2LP3TZqJh/Pm\nEYUFiQQeMB8GA1UdIwQYMBaAFAFV+jRT2LP3TZqJh/PmEYUFiQQeMAwGA1UdEwQF\nMAMBAf8wDQYJKoZIhvcNAQELBQADggEBACg/pAY55LQ/4+52CNnKtP9eqUBIeYYK\nyfpx7rt6t3BqOQmDmJCvlYXaX4qmmwxlIINJTX6qfVKZYbYbvnSn2dU33dMcQbgG\nwapCA7xxSj3LqXvc27QuaTjOdwpAPKyFKxR4aESmHJ7tWz3C4HPQSb0TvP5z7gY3\nxmDfEbsJ1V0N5sYwcSO9MtzVgdWyCEwgOXa5bLOMzC+DamJ8zYPntzsHG4GA7aXE\n2jZk9GVuWx92j1NVLPBm+6Xi/WstrEVrJaBuaQZLA1+9ghGALooZqqP0tu2eK3ZR\nj5/PqZ4LlXsoP4+Fjzcvr5JaRsEmHou3LLG+H0SzdrudKOOCisW6qHs=\n-----END CERTIFICATE-----"}, "tenda.etcd": {"etcd.endpoints": "************:2379"}}